<?php
/**
 * Admin Users Management
 *
 * This file contains the user management functionality for administrators.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Include email functions
require_once '../includes/email_functions.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Process user actions (activate, deactivate, delete)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect(BASE_URL . '/admin/users.php');
    }

    // Get action and user ID
    $action = sanitize_input($_POST['action'] ?? '');
    $user_id = sanitize_input($_POST['user_id'] ?? '');

    if (!empty($action) && !empty($user_id)) {
        try {
            // Get admin note if provided
            $admin_note = sanitize_input($_POST['admin_note'] ?? '');

            // Get current admin ID
            $admin_id = $_SESSION['admin_id'] ?? 1;

            // Get current user status before updating
            $stmt = $db->prepare("SELECT status FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $current_status = '';

            if ($result && $result->num_rows > 0) {
                $current_status = $result->fetch_assoc()['status'];
            }

            switch ($action) {
                case 'activate':
                    // Update user status
                    $db->query("UPDATE users SET status = 'active' WHERE id = $user_id");

                    // Record status change in history
                    $stmt = $db->prepare("INSERT INTO user_status_history (user_id, status, changed_by, admin_note) VALUES (?, 'active', ?, ?)");
                    $stmt->bind_param("iis", $user_id, $admin_id, $admin_note);
                    $stmt->execute();

                    // Send email notification
                    $email_sent = send_user_status_email($user_id, 'active', $admin_note);

                    // Set appropriate message based on previous status
                    if ($current_status === 'suspended') {
                        if ($email_sent) {
                            set_flash_message('success', 'User reactivated successfully after suspension and notification email sent.');
                        } else {
                            set_flash_message('success', 'User reactivated successfully after suspension but failed to send notification email.');
                        }
                    } else {
                        if ($email_sent) {
                            set_flash_message('success', 'User activated successfully and notification email sent.');
                        } else {
                            set_flash_message('success', 'User activated successfully but failed to send notification email.');
                        }
                    }
                    break;

                case 'deactivate':
                    // Update user status
                    $db->query("UPDATE users SET status = 'inactive' WHERE id = $user_id");

                    // Record status change in history
                    $stmt = $db->prepare("INSERT INTO user_status_history (user_id, status, changed_by, admin_note) VALUES (?, 'inactive', ?, ?)");
                    $stmt->bind_param("iis", $user_id, $admin_id, $admin_note);
                    $stmt->execute();

                    // Send email notification
                    $email_sent = send_user_status_email($user_id, 'inactive', $admin_note);

                    if ($email_sent) {
                        set_flash_message('success', 'User deactivated successfully and notification email sent.');
                    } else {
                        set_flash_message('success', 'User deactivated successfully but failed to send notification email.');
                    }
                    break;

                case 'suspend':
                    // Update user status
                    $db->query("UPDATE users SET status = 'suspended' WHERE id = $user_id");

                    // Record status change in history
                    $stmt = $db->prepare("INSERT INTO user_status_history (user_id, status, changed_by, admin_note) VALUES (?, 'suspended', ?, ?)");
                    $stmt->bind_param("iis", $user_id, $admin_id, $admin_note);
                    $stmt->execute();

                    // Send email notification
                    $email_sent = send_user_status_email($user_id, 'suspended', $admin_note);

                    if ($email_sent) {
                        set_flash_message('success', 'User suspended successfully and notification email sent.');
                    } else {
                        set_flash_message('success', 'User suspended successfully but failed to send notification email.');
                    }
                    break;

                case 'delete':
                    // Check if user has loan applications
                    $result = $db->query("SELECT COUNT(*) as count FROM loan_applications WHERE user_id = $user_id");
                    $row = $result->fetch_assoc();

                    if ($row['count'] > 0) {
                        set_flash_message('error', 'Cannot delete user with loan applications. Deactivate the user instead.');
                    } else {
                        $db->query("DELETE FROM users WHERE id = $user_id");
                        set_flash_message('success', 'User deleted successfully.');
                    }
                    break;

                default:
                    set_flash_message('error', 'Invalid action.');
                    break;
            }
        } catch (Exception $e) {
            error_log('User Action Error: ' . $e->getMessage());
            set_flash_message('error', 'An error occurred. Please try again later.');
        }
    }

    redirect(BASE_URL . '/admin/users.php');
}

// Get users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Get total users count
$result = $db->query("SELECT COUNT(*) as count FROM users");
$row = $result->fetch_assoc();
$total_users = $row['count'];
$total_pages = ceil($total_users / $limit);

// Get users for current page
$users = [];
$result = $db->query("
    SELECT u.*, c.code as currency_code, c.symbol as currency_symbol
    FROM users u
    LEFT JOIN currencies c ON u.currency_id = c.id
    ORDER BY u.id DESC
    LIMIT $offset, $limit
");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="users-management">
    <div class="page-header">
        <h1>User Management</h1>
        <div class="page-actions">
            <a href="add-user.php" class="button button-primary">
                <i class="fas fa-plus"></i> Add New User
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2>Users</h2>
            <div class="card-actions">
                <div class="search-box">
                    <input type="text" id="userSearch" placeholder="Search users...">
                    <button type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="filter-box">
                    <select id="statusFilter">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="card-content">
            <?php if (empty($users)): ?>
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>No Users Found</h3>
                    <p>There are no users in the system yet.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="data-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Currency</th>
                                <th>Registered</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr data-status="<?php echo $user['status']; ?>">
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo $user['currency_symbol'] . ' ' . htmlspecialchars($user['currency_code']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $user['status']; ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="action-btn edit-btn" title="Edit User">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                                </svg>
                                            </a>
                                            <a href="view-user.php?id=<?php echo $user['id']; ?>" class="action-btn view-btn" title="View User">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/>
                                                    <path d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8zm8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>
                                                </svg>
                                            </a>

                                            <?php if ($user['status'] === 'active'): ?>
                                                <button type="button" class="action-btn deactivate-btn user-status-btn"
                                                        data-id="<?php echo $user['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                        data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                        data-action="deactivate"
                                                        title="Deactivate User">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M13.879 10.414a2.501 2.501 0 0 0-3.465 3.465l3.465-3.465Zm.707.707-3.465 3.465a2.501 2.501 0 0 0 3.465-3.465Zm-4.56-1.096a3.5 3.5 0 1 1 4.949 4.95 3.5 3.5 0 0 1-4.95-4.95ZM11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm-9 8c0 1 1 1 1 1h5.256A4.493 4.493 0 0 1 8 12.5a4.49 4.49 0 0 1 1.544-3.393C9.077 9.038 8.564 9 8 9c-5 0-6 3-6 4Z"/>
                                                    </svg>
                                                </button>
                                                <button type="button" class="action-btn suspend-btn user-status-btn"
                                                        data-id="<?php echo $user['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                        data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                        data-action="suspend"
                                                        title="Suspend User">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                                        <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                                                    </svg>
                                                </button>
                                            <?php elseif ($user['status'] === 'inactive' || $user['status'] === 'suspended'): ?>
                                                <button type="button" class="action-btn activate-btn user-status-btn"
                                                        data-id="<?php echo $user['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                        data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                        data-action="activate"
                                                        title="Activate User">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Zm1.679-4.493-1.335 2.226a.75.75 0 0 1-1.174.144l-.774-.773a.5.5 0 0 1 .708-.708l.547.548 1.17-1.951a.5.5 0 1 1 .858.514ZM11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                        <path d="M2 13c0 1 1 1 1 1h5.256A4.493 4.493 0 0 1 8 12.5a4.49 4.49 0 0 1 1.544-3.393C9.077 9.038 8.564 9 8 9c-5 0-6 3-6 4Z"/>
                                                    </svg>
                                                </button>
                                            <?php endif; ?>

                                            <button type="button" class="action-btn delete-btn user-delete-btn"
                                                    data-id="<?php echo $user['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($user['name']); ?>"
                                                    data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                    title="Delete User">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                    <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="users.php?page=<?php echo $page - 1; ?>" class="pagination-item">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="users.php?page=<?php echo $i; ?>" class="pagination-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <a href="users.php?page=<?php echo $page + 1; ?>" class="pagination-item">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const userSearch = document.getElementById('userSearch');
        const statusFilter = document.getElementById('statusFilter');
        const usersTable = document.getElementById('usersTable');

        if (userSearch && statusFilter && usersTable) {
            // Search functionality
            userSearch.addEventListener('keyup', filterUsers);

            // Status filter
            statusFilter.addEventListener('change', filterUsers);

            function filterUsers() {
                const searchTerm = userSearch.value.toLowerCase();
                const statusValue = statusFilter.value.toLowerCase();
                const rows = usersTable.querySelectorAll('tbody tr');

                rows.forEach(function(row) {
                    const rowText = row.textContent.toLowerCase();
                    const rowStatus = row.getAttribute('data-status').toLowerCase();

                    const matchesSearch = searchTerm === '' || rowText.includes(searchTerm);
                    const matchesStatus = statusValue === '' || rowStatus === statusValue;

                    row.style.display = matchesSearch && matchesStatus ? '' : 'none';
                });
            }
        }
    });
</script>

<style>
    .users-management {
        margin-bottom: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

    .card-actions {
        display: flex;
        gap: 1rem;
    }

    .search-box {
        position: relative;
    }

    .search-box input {
        padding: 0.5rem 2.5rem 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
    }

    .search-box button {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 2.5rem;
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
    }

    .filter-box select {
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
    }

    .empty-state-icon {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .data-table th {
        font-weight: 600;
        color: #6b7280;
    }

    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 0.25rem;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-inactive {
        background-color: #fee2e2;
        color: #b91c1c;
    }

    .status-suspended {
        background-color: #fef3c7;
        color: #92400e;
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #fff;
    }

    .view-btn {
        background-color: #3b82f6;
    }

    .view-btn:hover {
        background-color: #2563eb;
    }

    .edit-btn {
        background-color: #10b981;
    }

    .edit-btn:hover {
        background-color: #059669;
    }

    .deactivate-btn {
        background-color: #f59e0b;
    }

    .deactivate-btn:hover {
        background-color: #d97706;
    }

    .suspend-btn {
        background-color: #6366f1;
    }

    .suspend-btn:hover {
        background-color: #4f46e5;
    }

    .activate-btn {
        background-color: #10b981;
    }

    .activate-btn:hover {
        background-color: #059669;
    }

    .delete-btn {
        background-color: #ef4444;
    }

    .delete-btn:hover {
        background-color: #dc2626;
    }

    .button-small {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .button-success {
        background-color: #10b981;
        border-color: #10b981;
        color: #fff;
    }

    .button-warning {
        background-color: #f59e0b;
        border-color: #f59e0b;
        color: #fff;
    }

    .button-danger {
        background-color: #ef4444;
        border-color: #ef4444;
        color: #fff;
    }

    .inline-form {
        display: inline;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .pagination-item {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        color: #374151;
        text-decoration: none;
    }

    .pagination-item.active {
        background-color: #4f46e5;
        border-color: #4f46e5;
        color: #fff;
    }

    .pagination-item:hover:not(.active) {
        background-color: #f9fafb;
    }
</style>

<!-- User Status Modal -->
<div class="modal" id="userStatusModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="status-modal-title">Update User Status</h3>
                <button type="button" class="close-button" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="user-preview">
                    <div class="preview-header">User Information</div>
                    <div class="preview-content">
                        <div class="preview-row">
                            <div class="preview-label">Name:</div>
                            <div class="preview-value" id="status-preview-name"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Email:</div>
                            <div class="preview-value" id="status-preview-email"></div>
                        </div>
                    </div>
                </div>

                <div class="confirmation-message" id="status-confirmation-message"></div>

                <form method="POST" action="users.php" id="userStatusForm">
                    <?php echo csrf_token_field(); ?>
                    <input type="hidden" name="action" id="status-action">
                    <input type="hidden" name="user_id" id="status-user-id">

                    <div class="form-group" id="admin-note-container">
                        <label for="admin-note">Message to User (Optional)</label>
                        <textarea id="admin-note" name="admin_note" class="form-control" rows="4" placeholder="Enter a message that will be included in the email notification to the user..."></textarea>
                        <small class="form-text text-muted">This message will be included in the email notification sent to the user.</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="button button-primary" id="status-confirm-btn">Confirm</button>
                        <button type="button" class="button button-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- User Delete Modal -->
<div class="modal" id="userDeleteModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Delete User</h3>
                <button type="button" class="close-button" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="user-preview">
                    <div class="preview-header">User Information</div>
                    <div class="preview-content">
                        <div class="preview-row">
                            <div class="preview-label">Name:</div>
                            <div class="preview-value" id="delete-preview-name"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Email:</div>
                            <div class="preview-value" id="delete-preview-email"></div>
                        </div>
                    </div>
                </div>

                <div class="confirmation-message warning-message">
                    <p><strong>Warning:</strong> This action cannot be undone. All data associated with this user will be permanently deleted.</p>
                    <p>Are you sure you want to delete this user?</p>
                </div>

                <form method="POST" action="users.php" id="userDeleteForm">
                    <?php echo csrf_token_field(); ?>
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" id="delete-user-id">

                    <div class="form-actions">
                        <button type="submit" class="button button-danger">Delete User</button>
                        <button type="button" class="button button-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        overflow: auto;
        padding: 2rem 1rem;
    }

    .modal-dialog {
        max-width: 450px;
        margin: 0 auto;
        position: relative;
    }

    .modal-content {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.25rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .close-button {
        background: none;
        border: none;
        font-size: 1.25rem;
        line-height: 1;
        color: #6b7280;
        cursor: pointer;
    }

    .modal-body {
        padding: 1.25rem;
    }

    .user-preview {
        background-color: #f9fafb;
        border-radius: 0.375rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .preview-header {
        background-color: #f3f4f6;
        padding: 0.75rem 1rem;
        font-weight: 600;
        border-bottom: 1px solid #e5e7eb;
    }

    .preview-content {
        padding: 1rem;
    }

    .preview-row {
        display: flex;
        margin-bottom: 0.5rem;
    }

    .preview-row:last-child {
        margin-bottom: 0;
    }

    .preview-label {
        width: 35%;
        font-weight: 600;
        color: #6b7280;
    }

    .preview-value {
        width: 65%;
    }

    .confirmation-message {
        margin-bottom: 1.5rem;
        padding: 1rem;
        border-radius: 0.375rem;
        background-color: #f3f4f6;
    }

    .warning-message {
        background-color: #fff5f5;
    }

    .form-actions {
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
    }

    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .button-primary {
        background-color: #4f46e5;
        color: #fff;
    }

    .button-primary:hover {
        background-color: #4338ca;
    }

    .button-secondary {
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

    .button-danger {
        background-color: #ef4444;
        color: #fff;
    }

    .button-danger:hover {
        background-color: #dc2626;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // User search and filter functionality
        const userSearch = document.getElementById('userSearch');
        const statusFilter = document.getElementById('statusFilter');
        const usersTable = document.getElementById('usersTable');

        if (userSearch && statusFilter && usersTable) {
            // Search functionality
            userSearch.addEventListener('keyup', filterUsers);

            // Status filter
            statusFilter.addEventListener('change', filterUsers);

            function filterUsers() {
                const searchTerm = userSearch.value.toLowerCase();
                const statusValue = statusFilter.value.toLowerCase();
                const rows = usersTable.querySelectorAll('tbody tr');

                rows.forEach(function(row) {
                    const rowText = row.textContent.toLowerCase();
                    const rowStatus = row.getAttribute('data-status').toLowerCase();

                    const matchesSearch = searchTerm === '' || rowText.includes(searchTerm);
                    const matchesStatus = statusValue === '' || rowStatus === statusValue;

                    row.style.display = matchesSearch && matchesStatus ? '' : 'none';
                });
            }
        }

        // User status modal
        const statusButtons = document.querySelectorAll('.user-status-btn');
        const statusModal = document.getElementById('userStatusModal');
        const statusModalTitle = document.getElementById('status-modal-title');
        const statusPreviewName = document.getElementById('status-preview-name');
        const statusPreviewEmail = document.getElementById('status-preview-email');
        const statusConfirmationMessage = document.getElementById('status-confirmation-message');
        const statusAction = document.getElementById('status-action');
        const statusUserId = document.getElementById('status-user-id');
        const statusConfirmBtn = document.getElementById('status-confirm-btn');

        // User delete modal
        const deleteButtons = document.querySelectorAll('.user-delete-btn');
        const deleteModal = document.getElementById('userDeleteModal');
        const deletePreviewName = document.getElementById('delete-preview-name');
        const deletePreviewEmail = document.getElementById('delete-preview-email');
        const deleteUserId = document.getElementById('delete-user-id');

        // Close buttons
        const closeButtons = document.querySelectorAll('[data-dismiss="modal"]');

        // Initialize status buttons
        statusButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const email = this.getAttribute('data-email');
                const action = this.getAttribute('data-action');

                // Set modal title based on action
                if (action === 'activate') {
                    statusModalTitle.textContent = 'Activate User';
                } else if (action === 'deactivate') {
                    statusModalTitle.textContent = 'Deactivate User';
                } else if (action === 'suspend') {
                    statusModalTitle.textContent = 'Suspend User';
                }

                // Set preview values
                statusPreviewName.textContent = name;
                statusPreviewEmail.textContent = email;

                // Set confirmation message based on action
                if (action === 'activate') {
                    statusConfirmationMessage.innerHTML = '<p>Are you sure you want to activate this user?</p><p>The user will be able to log in and use the system.</p>';
                } else if (action === 'deactivate') {
                    statusConfirmationMessage.innerHTML = '<p>Are you sure you want to deactivate this user?</p><p>The user will not be able to log in until reactivated.</p>';
                } else if (action === 'suspend') {
                    statusConfirmationMessage.innerHTML = '<p>Are you sure you want to suspend this user?</p><p>The user will not be able to log in until the suspension is lifted.</p><p>This is a temporary measure that can be used for users who have violated terms of service or need to be reviewed.</p>';
                }

                // Set form values
                statusAction.value = action;
                statusUserId.value = id;

                // Set button text and class based on action
                if (action === 'activate') {
                    statusConfirmBtn.textContent = 'Activate';
                    statusConfirmBtn.className = 'button button-primary';
                } else if (action === 'deactivate') {
                    statusConfirmBtn.textContent = 'Deactivate';
                    statusConfirmBtn.className = 'button button-warning';
                } else if (action === 'suspend') {
                    statusConfirmBtn.textContent = 'Suspend';
                    statusConfirmBtn.className = 'button button-danger';
                }

                // Show modal
                statusModal.style.display = 'block';
            });
        });

        // Initialize delete buttons
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const email = this.getAttribute('data-email');

                // Set preview values
                deletePreviewName.textContent = name;
                deletePreviewEmail.textContent = email;

                // Set form values
                deleteUserId.value = id;

                // Show modal
                deleteModal.style.display = 'block';
            });
        });

        // Close modals
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                statusModal.style.display = 'none';
                deleteModal.style.display = 'none';
            });
        });

        // Close modals when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === statusModal) {
                statusModal.style.display = 'none';
            }
            if (event.target === deleteModal) {
                deleteModal.style.display = 'none';
            }
        });
    });
</script>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>
