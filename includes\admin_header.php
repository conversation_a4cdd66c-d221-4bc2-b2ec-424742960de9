<?php
/**
 * Admin Header Template
 *
 * This file contains the header section of the admin panel.
 * It includes the HTML doctype, head section, and the top navigation.
 *
 * @package LendSwift
 */

// Prevent direct access to this file
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Get the current page filename
$current_page = basename($_SERVER['PHP_SELF']);

// Get unread support messages count
$unread_support_messages = 0;
try {
    $db = getDbConnection();
    $stmt = $db->prepare("
        SELECT COUNT(*) as count
        FROM support_messages m
        JOIN support_tickets t ON m.ticket_id = t.id
        WHERE m.sender_type = 'user' AND m.is_read = 0
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $unread_support_messages = $row['count'] ?? 0;
} catch (Exception $e) {
    // Log the error
    error_log('Error fetching unread support messages count: ' . $e->getMessage());
}

// Get unread admin notifications count
$unread_admin_notifications = 0;
try {
    $admin_id = get_current_admin_id();
    $stmt = $db->prepare("
        SELECT COUNT(*) as count
        FROM admin_notifications
        WHERE admin_id = ? AND is_read = 0
    ");
    $stmt->bind_param("i", $admin_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $unread_admin_notifications = $row['count'] ?? 0;
} catch (Exception $e) {
    // Log the error
    error_log('Error fetching unread admin notifications count: ' . $e->getMessage());
}

// Calculate total unread notifications
$total_unread_notifications = $unread_support_messages + $unread_admin_notifications;

/**
 * Convert a MySQL datetime to a "time ago" string
 *
 * @param string $datetime The MySQL datetime string
 * @return string The "time ago" string
 */
function time_elapsed_string($datetime) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    // Calculate weeks without using dynamic property
    $weeks = floor($diff->days / 7);
    $days = $diff->days % 7;

    $string = array();

    // Add each time unit if it's greater than 0
    if ($diff->y > 0) {
        $string['y'] = $diff->y . ' year' . ($diff->y > 1 ? 's' : '');
    }
    if ($diff->m > 0) {
        $string['m'] = $diff->m . ' month' . ($diff->m > 1 ? 's' : '');
    }
    if ($weeks > 0) {
        $string['w'] = $weeks . ' week' . ($weeks > 1 ? 's' : '');
    }
    if ($days > 0) {
        $string['d'] = $days . ' day' . ($days > 1 ? 's' : '');
    }
    if ($diff->h > 0) {
        $string['h'] = $diff->h . ' hour' . ($diff->h > 1 ? 's' : '');
    }
    if ($diff->i > 0) {
        $string['i'] = $diff->i . ' minute' . ($diff->i > 1 ? 's' : '');
    }
    if ($diff->s > 0) {
        $string['s'] = $diff->s . ' second' . ($diff->s > 1 ? 's' : '');
    }

    if (!empty($string)) {
        // Return the first time unit
        return reset($string) . ' ago';
    }

    return 'just now';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - <?php echo SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo ASSETS_URL; ?>/images/favicon.svg">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/admin.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/form-builder.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/notification-banner.css">

    <!-- Font Awesome (for icons) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Alpine.js (to be added later) -->
    <!-- <script defer src="<?php echo ASSETS_URL; ?>/js/alpine.min.js"></script> -->
</head>
<body class="admin-body">
    <div class="admin-container">
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <div class="admin-logo">
                    <a href="index.php">
                        <?php
                        // Get site logo and size settings
                        $site_logo = get_setting('site_logo', '/assets/images/logo.svg');
                        $logo_size = get_setting('logo_size', 'medium');
                        $logo_size_class = 'logo-' . $logo_size;
                        $logo_custom_size = get_setting('logo_custom_size', '100');
                        $show_site_name = get_setting('show_site_name', '0');

                        // Calculate custom size factor (percentage to decimal)
                        $size_factor = intval($logo_custom_size) / 100;

                        if (!empty($site_logo) && file_exists(BASE_PATH . $site_logo)) {
                            // If logo exists, show it with custom size
                            echo '<img src="' . BASE_URL . $site_logo . '" alt="' . SITE_NAME . '" class="' . $logo_size_class . '" style="--logo-size-factor: ' . $size_factor . ';">';

                            // Show site name if enabled
                            if ($show_site_name == '1') {
                                echo '<span>' . SITE_NAME . ' Admin</span>';
                            }
                        } else {
                            // If no logo, show site name
                            echo '<span>' . SITE_NAME . ' Admin</span>';
                        }
                        ?>
                    </a>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="admin-nav">
                <ul>
                    <li class="<?php echo $current_page === 'index.php' ? 'active' : ''; ?>">
                        <a href="index.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'users.php' ? 'active' : ''; ?>">
                        <a href="users.php">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'applications.php' ? 'active' : ''; ?>">
                        <a href="applications.php">
                            <i class="fas fa-file-alt"></i>
                            <span>Loan Applications</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'guest-conversion.php' ? 'active' : ''; ?>">
                        <a href="guest-conversion.php">
                            <i class="fas fa-user-plus"></i>
                            <span>Guest Conversion</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'loan-products.php' ? 'active' : ''; ?>">
                        <a href="loan-products.php">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Loan Products</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'transactions.php' ? 'active' : ''; ?>">
                        <a href="transactions.php">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Transactions</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'payment-methods.php' ? 'active' : ''; ?>">
                        <a href="payment-methods.php">
                            <i class="fas fa-credit-card"></i>
                            <span>Payment Methods</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'forms.php' ? 'active' : ''; ?>">
                        <a href="forms.php">
                            <i class="fas fa-wpforms"></i>
                            <span>Form Builder</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'statuses.php' ? 'active' : ''; ?>">
                        <a href="statuses.php">
                            <i class="fas fa-tasks"></i>
                            <span>Loan Statuses</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'currencies.php' ? 'active' : ''; ?>">
                        <a href="currencies.php">
                            <i class="fas fa-dollar-sign"></i>
                            <span>Currencies</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'email-templates.php' ? 'active' : ''; ?>">
                        <a href="email-templates.php">
                            <i class="fas fa-envelope"></i>
                            <span>Email Templates</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'services-content.php' ? 'active' : ''; ?>">
                        <a href="services-content.php">
                            <i class="fas fa-file-alt"></i>
                            <span>Services Content</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'support.php' ? 'active' : ''; ?>">
                        <a href="support.php">
                            <i class="fas fa-headset"></i>
                            <span>Support Tickets</span>
                            <?php if ($unread_support_messages > 0): ?>
                                <span class="nav-badge"><?php echo $unread_support_messages; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'notifications.php' ? 'active' : ''; ?>">
                        <a href="notifications.php">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'application-reports.php' ? 'active' : ''; ?>">
                        <a href="application-reports.php">
                            <i class="fas fa-chart-bar"></i>
                            <span>Application Reports</span>
                        </a>
                    </li>
                    <li class="<?php echo $current_page === 'settings.php' ? 'active' : ''; ?>">
                        <a href="settings.php">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="sidebar-logout">
                        <a href="logout.php" class="logout-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <div class="admin-content">
            <header class="admin-header">
                <div class="header-search">
                    <form id="headerSearchForm" action="search.php" method="GET">
                        <input type="text" name="q" placeholder="Search..." autocomplete="off">
                        <button type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <div class="header-actions">
                    <a href="<?php echo BASE_URL; ?>" class="header-action" title="View Website" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                    </a>

                    <div class="header-action notifications">
                        <i class="fas fa-bell"></i>
                        <?php if ($total_unread_notifications > 0): ?>
                            <span class="badge"><?php echo $total_unread_notifications; ?></span>
                        <?php endif; ?>

                        <div class="dropdown-menu">
                            <div class="dropdown-header">
                                <h3>Notifications</h3>
                                <a href="notifications.php">View All</a>
                            </div>
                            <div class="dropdown-content">
                                <?php
                                // Get latest notifications
                                $latest_notifications = [];
                                try {
                                    $admin_id = get_current_admin_id();
                                    $stmt = $db->prepare("
                                        SELECT id, title, message, type, link, created_at
                                        FROM admin_notifications
                                        WHERE admin_id = ?
                                        ORDER BY created_at DESC
                                        LIMIT 3
                                    ");
                                    $stmt->bind_param("i", $admin_id);
                                    $stmt->execute();
                                    $result = $stmt->get_result();

                                    if ($result) {
                                        $latest_notifications = $result->fetch_all(MYSQLI_ASSOC);
                                    }
                                } catch (Exception $e) {
                                    error_log('Error fetching latest notifications: ' . $e->getMessage());
                                }

                                if (!empty($latest_notifications)):
                                    foreach ($latest_notifications as $notification):
                                        $icon_class = 'fas fa-bell';

                                        if ($notification['type'] === 'support') {
                                            $icon_class = 'fas fa-headset';
                                        } elseif ($notification['type'] === 'application') {
                                            $icon_class = 'fas fa-file-alt';
                                        } elseif ($notification['type'] === 'document') {
                                            $icon_class = 'fas fa-file-upload';
                                        } elseif ($notification['type'] === 'transaction') {
                                            $icon_class = 'fas fa-money-bill-wave';
                                        } elseif ($notification['type'] === 'user') {
                                            $icon_class = 'fas fa-user';
                                        }

                                        $link = !empty($notification['link']) ? $notification['link'] : 'notifications.php';
                                ?>
                                <a href="<?php echo $link; ?>" class="notification-item">
                                    <div class="notification-icon">
                                        <i class="<?php echo $icon_class; ?>"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p><?php echo htmlspecialchars($notification['title']); ?></p>
                                        <span class="notification-time"><?php echo time_elapsed_string($notification['created_at']); ?></span>
                                    </div>
                                </a>
                                <?php
                                    endforeach;
                                else:
                                ?>
                                <div class="empty-dropdown">
                                    <p>No new notifications</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="header-action user-profile">
                        <img src="<?php echo ASSETS_URL; ?>/images/admin-avatar.svg" alt="Admin Avatar">
                        <span><?php echo $_SESSION['admin_name'] ?? 'Administrator'; ?></span>

                        <div class="dropdown-menu">
                            <a href="profile.php">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="settings.php">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <a href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="admin-main">
                <?php echo display_flash_message(); ?>
