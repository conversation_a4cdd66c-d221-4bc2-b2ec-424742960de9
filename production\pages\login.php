<?php
/**
 * User Login Page
 *
 * This file contains the login form for users.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
if (!defined('LENDSWIFT')) {
    // If accessed directly, use relative path
    require_once '../includes/init.php';
}

// Check if the user is already logged in
if (is_user_logged_in()) {
    redirect(BASE_URL . '/?page=dashboard');
}

// Initialize variables
$errors = [];
$email = '';

// Process login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect(BASE_URL . '/?page=login');
    }

    // Get form data
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']) ? true : false;

    // Validate form data
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    }

    if (empty($password)) {
        $errors[] = 'Password is required.';
    }

    // If no validation errors, attempt to log in
    if (empty($errors)) {
        try {
            // Get database connection
            $db = getDbConnection();

            // Prepare statement
            $stmt = $db->prepare("SELECT id, name, email, password, status FROM users WHERE email = ?");

            if (!$stmt) {
                throw new Exception("Database error: " . $db->error);
            }

            // Bind parameters and execute
            $stmt->bind_param("s", $email);
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();

            // Close statement
            $stmt->close();

            // Check if user exists and password is correct
            if ($user && password_verify($password, $user['password'])) {
                // Check if user is active
                if ($user['status'] === 'inactive') {
                    $errors[] = 'Your account is inactive. Please contact support.';
                } else {
                    // Get user's currency information
                    $currency_stmt = $db->prepare("
                        SELECT c.id, c.code, c.symbol, c.name
                        FROM users u
                        JOIN currencies c ON u.currency_id = c.id
                        WHERE u.id = ?
                    ");
                    $currency_stmt->bind_param("i", $user['id']);
                    $currency_stmt->execute();
                    $currency_result = $currency_stmt->get_result();
                    $currency = $currency_result->fetch_assoc();

                    // If no currency found, use default
                    if (!$currency) {
                        $currency = [
                            'id' => DEFAULT_CURRENCY_ID,
                            'code' => 'USD',
                            'symbol' => '$',
                            'name' => 'US Dollar'
                        ];
                    }

                    // Set session variables
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_currency'] = $currency;

                    // Set remember me cookie if requested
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        $expires = time() + (30 * 24 * 60 * 60); // 30 days

                        // Store token in database
                        $stmt = $db->prepare("INSERT INTO user_tokens (user_id, token, expires) VALUES (?, ?, FROM_UNIXTIME(?))");
                        $stmt->bind_param("isi", $user['id'], $token, $expires);
                        $stmt->execute();

                        // Set cookie
                        setcookie('remember_token', $token, $expires, '/', '', false, true);
                    }

                    // Log login attempt
                    $ip = $_SERVER['REMOTE_ADDR'];
                    $stmt = $db->prepare("INSERT INTO login_logs (user_id, ip_address, status) VALUES (?, ?, 'success')");
                    $stmt->bind_param("is", $user['id'], $ip);
                    $stmt->execute();

                    // Redirect to dashboard
                    set_flash_message('success', 'You have been logged in successfully.');
                    redirect(BASE_URL . '/?page=dashboard');
                }
            } else {
                // Invalid credentials
                $errors[] = 'Invalid email or password.';

                // Log failed login attempt
                $ip = $_SERVER['REMOTE_ADDR'];
                $stmt = $db->prepare("INSERT INTO login_logs (email, ip_address, status) VALUES (?, ?, 'failed')");
                $stmt->bind_param("ss", $email, $ip);
                $stmt->execute();
            }
        } catch (Exception $e) {
            // Log the error
            error_log('User Login Error: ' . $e->getMessage());

            // Show generic error message
            $errors[] = 'An error occurred. Please try again later.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo ASSETS_URL; ?>/images/favicon.svg">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/auth.css">

    <!-- Font Awesome (for icons) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Google Translate Script -->
    <script type="text/javascript">
        function googleTranslateElementInit() {
            if (document.getElementById('google_translate_element_login')) {
                new google.translate.TranslateElement({
                    pageLanguage: 'en',
                    includedLanguages: 'en,es,fr,de,zh-CN,pt,ru,ar,it,ja,ko,hi,nl,sv,no,da,fi,th',
                    layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                }, 'google_translate_element_login');
            }
        }
    </script>
    <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --primary-light: #e0e7ff;
            --text-color: #1f2937;
            --text-muted: #6b7280;
            --border-color: #e5e7eb;
            --background-color: #f9fafb;
            --card-background: #ffffff;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
        }

        body {
            background-color: var(--background-color);
            background-image:
                radial-gradient(circle at 25px 25px, rgba(79, 70, 229, 0.05) 2%, transparent 0%),
                radial-gradient(circle at 75px 75px, rgba(79, 70, 229, 0.05) 2%, transparent 0%);
            background-size: 100px 100px;
            font-family: 'Inter', sans-serif;
            color: var(--text-color);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
        }

        .login-container {
            width: 100%;
            height: 100vh;
            background-color: var(--card-background);
            overflow: hidden;
            display: flex;
        }

        .login-form-container {
            width: 50%;
            padding: 2rem 4rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .login-image-container {
            width: 50%;
            background-color: #4f46e5;
            background-image: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-logo {
            position: absolute;
            top: 2rem;
            left: 4rem;
        }

        .login-logo img {
            height: 50px;
            width: auto;
        }

        .login-header {
            margin-bottom: 2rem;
            text-align: left;
            width: 80%;
            max-width: 400px;
        }

        form {
            width: 80%;
            max-width: 400px;
            margin: 0 auto;
        }

        .login-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
        }

        .login-header p {
            color: var(--text-muted);
            margin: 0;
            font-size: 0.85rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .form-group .input-wrapper {
            position: relative;
        }

        .form-group .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-color);
        }

        .form-group .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-color);
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            box-sizing: border-box;
            height: 45px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.875rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
        }

        .remember-me input {
            margin-right: 0.5rem;
        }

        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .login-button {
            width: 100%;
            padding: 0.75rem 1rem;
            background-color: var(--primary-color);
            background-image: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
        }

        .login-button:hover {
            background-color: var(--primary-hover);
            box-shadow: 0 6px 8px rgba(79, 70, 229, 0.3);
            transform: translateY(-1px);
        }

        .login-divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .login-divider::before,
        .login-divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background-color: var(--border-color);
        }

        .login-divider::before {
            margin-right: 1rem;
        }

        .login-divider::after {
            margin-left: 1rem;
        }

        .register-link {
            text-align: center;
            font-size: 0.875rem;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .dashboard-text h2 {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
        }

        .dashboard-text p {
            font-size: 1rem;
            margin-bottom: 2rem;
            color: white;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
            font-size: 0.875rem;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: #b91c1c;
            border: 1px solid #fecaca;
        }

        .alert-success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        /* Google Translate Styles for Login Page */
        .google-translate-login {
            position: absolute;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
        }

        /* Hide Google Translate banner */
        .goog-te-banner-frame {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
        }

        /* Style language selector */
        .goog-te-gadget {
            font-size: 0 !important; /* Hide text */
        }

        .goog-te-combo {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            color: #333;
            font-size: 14px;
            width: 140px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .goog-te-combo:hover {
            border-color: var(--primary-color);
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 6px 16px rgba(79, 70, 229, 0.2);
        }

        .goog-te-combo:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        @media (max-width: 992px) {
            .login-container {
                flex-direction: column;
                height: auto;
            }

            .login-form-container,
            .login-image-container {
                width: 100%;
            }

            .login-image-container {
                order: -1;
                padding: 2rem;
                min-height: 300px;
            }

            .login-form-container {
                padding: 2rem;
                padding-top: 5rem; /* Add space for the logo */
            }

            .login-logo {
                top: 1rem;
                left: 2rem;
            }

            .google-translate-login {
                top: 1rem;
                right: 1rem;
            }

            .goog-te-combo {
                width: 120px;
                font-size: 12px;
                padding: 6px 10px;
            }
        }

        @media (max-width: 576px) {
            .login-form-container,
            .login-image-container {
                padding: 1.5rem;
            }

            .login-form-container {
                padding-top: 4.5rem; /* Space for logo */
            }

            .login-image-container {
                min-height: 250px;
            }

            form {
                width: 90%;
                max-width: 100%;
            }

            .login-logo {
                top: 1rem;
                left: 1.5rem;
            }

            .login-logo img {
                height: 40px;
            }

            .google-translate-login {
                top: 0.75rem;
                right: 1rem;
            }

            .goog-te-combo {
                width: 100px;
                font-size: 11px;
                padding: 5px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form-container">
            <div class="login-logo">
                <img src="<?php echo ASSETS_URL; ?>/images/logo.svg" alt="<?php echo SITE_NAME; ?>">
            </div>

            <!-- Google Translate Widget -->
            <?php if (get_setting('google_translate_login_enabled', '1') == '1'): ?>
                <div id="google_translate_element_login" class="google-translate-login"></div>
            <?php endif; ?>

            <div class="login-header">
                <h1>Welcome Back</h1>
                <p>Enter your User credentials.</p>
            </div>

            <?php echo display_flash_message(); ?>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo BASE_URL; ?>/?page=login" data-validate="true">
                <?php echo csrf_token_field(); ?>

                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-wrapper">
                        <span class="input-icon">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="email" id="email" name="email" placeholder="Enter your email" value="<?php echo htmlspecialchars($email); ?>" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-wrapper">
                        <span class="input-icon">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        <button type="button" class="password-toggle" onclick="togglePasswordVisibility()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="remember-forgot">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember Me</label>
                    </div>
                    <div class="forgot-password">
                        <a href="<?php echo BASE_URL; ?>/?page=forgot-password">Forgot Password?</a>
                    </div>
                </div>

                <button type="submit" name="login" class="login-button">
                    Log In
                </button>

                <div class="login-divider">User Access Only</div>

                <div class="register-link">
                    <a href="<?php echo BASE_URL; ?>/">← Back to Website</a>
                </div>
            </form>
        </div>

        <div class="login-image-container">
            <div class="dashboard-text">
                <h2>Effortlessly manage your loans and applications.</h2>
                <p>Log in to access your user dashboard and track your loan application process.</p>
            </div>
            <div class="dashboard-preview">
                <!-- Dashboard preview image with more realistic elements -->
                <div style="background-color: rgba(255, 255, 255, 0.1); border-radius: 8px; padding: 20px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
                    <!-- Header with search and user profile -->
                    <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                        <div style="background-color: rgba(255, 255, 255, 0.2); width: 40%; height: 40px; border-radius: 8px; display: flex; align-items: center; padding: 0 10px;">
                            <div style="width: 16px; height: 16px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.5); margin-right: 8px;"></div>
                            <div style="flex-grow: 1; height: 10px; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px;"></div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 20px; height: 20px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.3);"></div>
                            <div style="width: 20px; height: 20px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.3);"></div>
                            <div style="width: 36px; height: 36px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.4); border: 2px solid rgba(255, 255, 255, 0.6);"></div>
                        </div>
                    </div>

                    <!-- Stats cards -->
                    <div style="display: flex; justify-content: space-between; margin-bottom: 20px; gap: 15px;">
                        <div style="background-color: rgba(255, 255, 255, 0.15); width: 25%; height: 100px; border-radius: 8px; padding: 15px; box-sizing: border-box;">
                            <div style="height: 24px; width: 24px; border-radius: 6px; background-color: rgba(255, 255, 255, 0.3); margin-bottom: 10px;"></div>
                            <div style="height: 10px; width: 60%; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-bottom: 8px;"></div>
                            <div style="height: 18px; width: 40%; background-color: rgba(255, 255, 255, 0.5); border-radius: 5px;"></div>
                        </div>
                        <div style="background-color: rgba(255, 255, 255, 0.15); width: 25%; height: 100px; border-radius: 8px; padding: 15px; box-sizing: border-box;">
                            <div style="height: 24px; width: 24px; border-radius: 6px; background-color: rgba(255, 255, 255, 0.3); margin-bottom: 10px;"></div>
                            <div style="height: 10px; width: 60%; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-bottom: 8px;"></div>
                            <div style="height: 18px; width: 40%; background-color: rgba(255, 255, 255, 0.5); border-radius: 5px;"></div>
                        </div>
                        <div style="background-color: rgba(255, 255, 255, 0.15); width: 25%; height: 100px; border-radius: 8px; padding: 15px; box-sizing: border-box;">
                            <div style="height: 24px; width: 24px; border-radius: 6px; background-color: rgba(255, 255, 255, 0.3); margin-bottom: 10px;"></div>
                            <div style="height: 10px; width: 60%; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-bottom: 8px;"></div>
                            <div style="height: 18px; width: 40%; background-color: rgba(255, 255, 255, 0.5); border-radius: 5px;"></div>
                        </div>
                        <div style="background-color: rgba(255, 255, 255, 0.15); width: 25%; height: 100px; border-radius: 8px; padding: 15px; box-sizing: border-box;">
                            <div style="height: 24px; width: 24px; border-radius: 6px; background-color: rgba(255, 255, 255, 0.3); margin-bottom: 10px;"></div>
                            <div style="height: 10px; width: 60%; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-bottom: 8px;"></div>
                            <div style="height: 18px; width: 40%; background-color: rgba(255, 255, 255, 0.5); border-radius: 5px;"></div>
                        </div>
                    </div>

                    <!-- Table with recent applications -->
                    <div style="background-color: rgba(255, 255, 255, 0.15); width: 100%; border-radius: 8px; padding: 15px; margin-bottom: 20px; box-sizing: border-box;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="height: 14px; width: 40%; background-color: rgba(255, 255, 255, 0.4); border-radius: 5px;"></div>
                            <div style="height: 26px; width: 100px; background-color: rgba(255, 255, 255, 0.25); border-radius: 5px;"></div>
                        </div>

                        <!-- Table header -->
                        <div style="display: flex; padding: 10px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <div style="width: 10%; height: 10px; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px;"></div>
                            <div style="width: 25%; height: 10px; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-left: 15px;"></div>
                            <div style="width: 20%; height: 10px; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-left: 15px;"></div>
                            <div style="width: 15%; height: 10px; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-left: 15px;"></div>
                            <div style="width: 15%; height: 10px; background-color: rgba(255, 255, 255, 0.3); border-radius: 5px; margin-left: 15px;"></div>
                        </div>

                        <!-- Table rows -->
                        <?php for ($i = 0; $i < 3; $i++): ?>
                        <div style="display: flex; padding: 12px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1); align-items: center;">
                            <div style="width: 10%; height: 10px; background-color: rgba(255, 255, 255, 0.2); border-radius: 5px;"></div>
                            <div style="width: 25%; height: 10px; background-color: rgba(255, 255, 255, 0.2); border-radius: 5px; margin-left: 15px;"></div>
                            <div style="width: 20%; height: 10px; background-color: rgba(255, 255, 255, 0.2); border-radius: 5px; margin-left: 15px;"></div>
                            <div style="width: 15%; height: 22px; background-color: rgba(<?php echo $i == 0 ? '100, 230, 160, 0.3' : ($i == 1 ? '255, 170, 80, 0.3' : '120, 160, 255, 0.3'); ?>); border-radius: 5px; margin-left: 15px;"></div>
                            <div style="width: 15%; display: flex; margin-left: 15px;">
                                <div style="width: 60px; height: 24px; background-color: rgba(255, 255, 255, 0.25); border-radius: 5px;"></div>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/auth.js"></script>
    <script>
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const passwordToggle = document.querySelector('.password-toggle i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.classList.remove('fa-eye');
                passwordToggle.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordToggle.classList.remove('fa-eye-slash');
                passwordToggle.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>