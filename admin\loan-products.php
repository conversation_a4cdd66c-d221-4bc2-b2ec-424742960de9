<?php
/**
 * Admin Loan Products Management
 * 
 * This file contains the loan products management functionality for administrators.
 * 
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect(BASE_URL . '/admin/loan-products.php');
    }
    
    $action = sanitize_input($_POST['action'] ?? '');
    
    if ($action === 'add_product') {
        $name = sanitize_input($_POST['name'] ?? '');
        $min_amount = (float)($_POST['min_amount'] ?? 0);
        $max_amount = (float)($_POST['max_amount'] ?? 0);
        $interest_rate = (float)($_POST['interest_rate'] ?? 0);
        $term_months = (int)($_POST['term_months'] ?? 0);
        
        // Validate input
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'Product name is required.';
        }
        
        if ($min_amount <= 0) {
            $errors[] = 'Minimum amount must be greater than zero.';
        }
        
        if ($max_amount <= 0) {
            $errors[] = 'Maximum amount must be greater than zero.';
        }
        
        if ($max_amount <= $min_amount) {
            $errors[] = 'Maximum amount must be greater than minimum amount.';
        }
        
        if ($interest_rate <= 0) {
            $errors[] = 'Interest rate must be greater than zero.';
        }
        
        if ($term_months <= 0) {
            $errors[] = 'Term months must be greater than zero.';
        }
        
        if (empty($errors)) {
            try {
                // Insert new loan product
                $stmt = $db->prepare("
                    INSERT INTO loan_products (name, min_amount, max_amount, interest_rate, term_months)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $stmt->bind_param("sdddi", $name, $min_amount, $max_amount, $interest_rate, $term_months);
                $stmt->execute();
                
                set_flash_message('success', 'Loan product added successfully.');
                redirect(BASE_URL . '/admin/loan-products.php');
            } catch (Exception $e) {
                error_log('Add Loan Product Error: ' . $e->getMessage());
                set_flash_message('error', 'An error occurred. Please try again later.');
            }
        } else {
            // Store errors in session
            $_SESSION['form_errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect(BASE_URL . '/admin/loan-products.php');
        }
    } elseif ($action === 'edit_product') {
        $product_id = (int)($_POST['product_id'] ?? 0);
        $name = sanitize_input($_POST['name'] ?? '');
        $min_amount = (float)($_POST['min_amount'] ?? 0);
        $max_amount = (float)($_POST['max_amount'] ?? 0);
        $interest_rate = (float)($_POST['interest_rate'] ?? 0);
        $term_months = (int)($_POST['term_months'] ?? 0);
        
        // Validate input
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'Product name is required.';
        }
        
        if ($min_amount <= 0) {
            $errors[] = 'Minimum amount must be greater than zero.';
        }
        
        if ($max_amount <= 0) {
            $errors[] = 'Maximum amount must be greater than zero.';
        }
        
        if ($max_amount <= $min_amount) {
            $errors[] = 'Maximum amount must be greater than minimum amount.';
        }
        
        if ($interest_rate <= 0) {
            $errors[] = 'Interest rate must be greater than zero.';
        }
        
        if ($term_months <= 0) {
            $errors[] = 'Term months must be greater than zero.';
        }
        
        if (empty($errors)) {
            try {
                // Update loan product
                $stmt = $db->prepare("
                    UPDATE loan_products
                    SET name = ?, min_amount = ?, max_amount = ?, interest_rate = ?, term_months = ?
                    WHERE id = ?
                ");
                
                $stmt->bind_param("sdddii", $name, $min_amount, $max_amount, $interest_rate, $term_months, $product_id);
                $stmt->execute();
                
                set_flash_message('success', 'Loan product updated successfully.');
                redirect(BASE_URL . '/admin/loan-products.php');
            } catch (Exception $e) {
                error_log('Edit Loan Product Error: ' . $e->getMessage());
                set_flash_message('error', 'An error occurred. Please try again later.');
            }
        } else {
            // Store errors in session
            $_SESSION['form_errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            redirect(BASE_URL . '/admin/loan-products.php?edit=' . $product_id);
        }
    } elseif ($action === 'delete_product') {
        $product_id = (int)($_POST['product_id'] ?? 0);
        
        try {
            // Check if product is used in any loan applications
            $result = $db->query("SELECT COUNT(*) as count FROM loan_applications WHERE loan_product_id = $product_id");
            $row = $result->fetch_assoc();
            
            if ($row['count'] > 0) {
                set_flash_message('error', 'Cannot delete product that is used in loan applications.');
                redirect(BASE_URL . '/admin/loan-products.php');
            }
            
            // Delete loan product
            $db->query("DELETE FROM loan_products WHERE id = $product_id");
            
            set_flash_message('success', 'Loan product deleted successfully.');
            redirect(BASE_URL . '/admin/loan-products.php');
        } catch (Exception $e) {
            error_log('Delete Loan Product Error: ' . $e->getMessage());
            set_flash_message('error', 'An error occurred. Please try again later.');
        }
    }
}

// Get loan products
$products = [];
$result = $db->query("SELECT * FROM loan_products ORDER BY name ASC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $products[] = $row;
    }
}

// Check if we're editing a product
$edit_mode = false;
$edit_product = null;

if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $product_id = (int)$_GET['edit'];
    
    foreach ($products as $product) {
        if ($product['id'] == $product_id) {
            $edit_product = $product;
            $edit_mode = true;
            break;
        }
    }
}

// Get form errors and data
$form_errors = $_SESSION['form_errors'] ?? [];
$form_data = $_SESSION['form_data'] ?? [];

// Clear session data
unset($_SESSION['form_errors']);
unset($_SESSION['form_data']);

// Include admin header
include '../includes/admin_header.php';
?>

<div class="loan-products-management">
    <div class="page-header">
        <h1><?php echo $edit_mode ? 'Edit Loan Product' : 'Loan Products'; ?></h1>
        <?php if (!$edit_mode): ?>
            <div class="page-actions">
                <button type="button" class="button button-primary" data-toggle="modal" data-target="addProductModal">
                    <i class="fas fa-plus"></i> Add New Product
                </button>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if ($edit_mode): ?>
        <div class="card">
            <div class="card-header">
                <h2>Edit Loan Product</h2>
                <div class="card-actions">
                    <a href="loan-products.php" class="button button-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Products
                    </a>
                </div>
            </div>
            <div class="card-content">
                <?php if (!empty($form_errors)): ?>
                    <div class="alert alert-danger">
                        <ul>
                            <?php foreach ($form_errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="loan-products.php">
                    <?php echo csrf_token_field(); ?>
                    <input type="hidden" name="action" value="edit_product">
                    <input type="hidden" name="product_id" value="<?php echo $edit_product['id']; ?>">
                    
                    <div class="form-group">
                        <label for="name">Product Name <span class="required">*</span></label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo htmlspecialchars($form_data['name'] ?? $edit_product['name']); ?>" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="min_amount">Minimum Amount <span class="required">*</span></label>
                            <input type="number" id="min_amount" name="min_amount" class="form-control" min="0" step="0.01" value="<?php echo $form_data['min_amount'] ?? $edit_product['min_amount']; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="max_amount">Maximum Amount <span class="required">*</span></label>
                            <input type="number" id="max_amount" name="max_amount" class="form-control" min="0" step="0.01" value="<?php echo $form_data['max_amount'] ?? $edit_product['max_amount']; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="interest_rate">Interest Rate (%) <span class="required">*</span></label>
                            <input type="number" id="interest_rate" name="interest_rate" class="form-control" min="0" step="0.01" value="<?php echo $form_data['interest_rate'] ?? $edit_product['interest_rate']; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="term_months">Term (Months) <span class="required">*</span></label>
                            <input type="number" id="term_months" name="term_months" class="form-control" min="1" step="1" value="<?php echo $form_data['term_months'] ?? $edit_product['term_months']; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="button button-primary">Update Product</button>
                        <a href="loan-products.php" class="button button-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-header">
                <h2>All Loan Products</h2>
            </div>
            <div class="card-content">
                <?php if (empty($products)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <h3>No Loan Products</h3>
                        <p>There are no loan products in the system yet.</p>
                        <button type="button" class="button button-primary" data-toggle="modal" data-target="addProductModal">
                            Add New Product
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Amount Range</th>
                                    <th>Interest Rate</th>
                                    <th>Term</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td><?php echo $product['id']; ?></td>
                                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                                        <td>$<?php echo number_format($product['min_amount'], 2); ?> - $<?php echo number_format($product['max_amount'], 2); ?></td>
                                        <td><?php echo $product['interest_rate']; ?>%</td>
                                        <td><?php echo $product['term_months']; ?> months</td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="loan-products.php?edit=<?php echo $product['id']; ?>" class="button button-small" title="Edit Product">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <form method="POST" action="loan-products.php" class="inline-form" onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.');">
                                                    <?php echo csrf_token_field(); ?>
                                                    <input type="hidden" name="action" value="delete_product">
                                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                    <button type="submit" class="button button-small button-danger" title="Delete Product">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Add Product Modal -->
<div class="modal" id="addProductModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Loan Product</h3>
                <button type="button" class="close-button" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <?php if (!empty($form_errors) && !$edit_mode): ?>
                    <div class="alert alert-danger">
                        <ul>
                            <?php foreach ($form_errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="loan-products.php">
                    <?php echo csrf_token_field(); ?>
                    <input type="hidden" name="action" value="add_product">
                    
                    <div class="form-group">
                        <label for="modal_name">Product Name <span class="required">*</span></label>
                        <input type="text" id="modal_name" name="name" class="form-control" value="<?php echo htmlspecialchars($form_data['name'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_min_amount">Minimum Amount <span class="required">*</span></label>
                            <input type="number" id="modal_min_amount" name="min_amount" class="form-control" min="0" step="0.01" value="<?php echo $form_data['min_amount'] ?? '1000'; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_max_amount">Maximum Amount <span class="required">*</span></label>
                            <input type="number" id="modal_max_amount" name="max_amount" class="form-control" min="0" step="0.01" value="<?php echo $form_data['max_amount'] ?? '10000'; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_interest_rate">Interest Rate (%) <span class="required">*</span></label>
                            <input type="number" id="modal_interest_rate" name="interest_rate" class="form-control" min="0" step="0.01" value="<?php echo $form_data['interest_rate'] ?? '10'; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_term_months">Term (Months) <span class="required">*</span></label>
                            <input type="number" id="modal_term_months" name="term_months" class="form-control" min="1" step="1" value="<?php echo $form_data['term_months'] ?? '12'; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="button button-primary">Add Product</button>
                        <button type="button" class="button button-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .loan-products-management {
        margin-bottom: 2rem;
    }
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }
    
    .card-content {
        padding: 1.5rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .data-table th {
        font-weight: 600;
        color: #6b7280;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .button-small {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .button-danger {
        background-color: #ef4444;
        border-color: #ef4444;
        color: #fff;
    }
    
    .inline-form {
        display: inline;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-group:last-child {
        margin-bottom: 0;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        font-size: 1rem;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .required {
        color: #ef4444;
    }
    
    .alert {
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0.25rem;
    }
    
    .alert-danger {
        background-color: #fee2e2;
        color: #b91c1c;
        border: 1px solid #fecaca;
    }
    
    .alert ul {
        margin: 0;
        padding-left: 1.5rem;
    }
    
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        overflow: auto;
    }
    
    .modal-dialog {
        margin: 2rem auto;
        max-width: 500px;
    }
    
    .modal-content {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .modal-header h3 {
        margin: 0;
    }
    
    .close-button {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Modal functionality
        const modalToggles = document.querySelectorAll('[data-toggle="modal"]');
        const modalDismiss = document.querySelectorAll('[data-dismiss="modal"]');
        
        modalToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const modal = document.getElementById(targetId);
                
                if (modal) {
                    modal.style.display = 'block';
                }
            });
        });
        
        modalDismiss.forEach(function(dismiss) {
            dismiss.addEventListener('click', function() {
                const modal = this.closest('.modal');
                
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
        
        <?php if (!empty($form_errors) && !$edit_mode): ?>
        // Show add product modal if there are form errors
        const addProductModal = document.getElementById('addProductModal');
        if (addProductModal) {
            addProductModal.style.display = 'block';
        }
        <?php endif; ?>
    });
</script>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>
