<?php
/**
 * Admin Settings
 *
 * This file contains the settings management functionality for administrators.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect('login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Define settings categories
$categories = [
    'general' => 'General Settings',
    'appearance' => 'Appearance Settings',
    'email' => 'Email Settings',
    'security' => 'Security Settings'
];

// Get active category
$active_category = isset($_GET['category']) && array_key_exists($_GET['category'], $categories)
    ? $_GET['category']
    : 'general';

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect('settings.php?category=' . $active_category);
    }

    if (isset($_POST['update_settings'])) {
        // Update settings
        foreach ($_POST['settings'] as $key => $value) {
            // Sanitize input
            $key = sanitize_input($key);
            $value = sanitize_input($value);

            // Update setting
            update_setting($key, $value);
        }

        set_flash_message('success', 'Settings updated successfully.');
        redirect('settings.php?category=' . $active_category);
    } elseif (isset($_POST['upload_logo'])) {
        // Handle logo upload
        if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
            $file_name = $_FILES['logo_file']['name'];
            $file_tmp = $_FILES['logo_file']['tmp_name'];
            $file_size = $_FILES['logo_file']['size'];
            $file_type = $_FILES['logo_file']['type'];

            // Get file extension
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            // Check file extension
            $allowed_extensions = ['svg', 'png', 'jpg', 'jpeg'];

            if (in_array($file_ext, $allowed_extensions)) {
                // Check file size (2MB max)
                $max_size = 2 * 1024 * 1024;

                if ($file_size <= $max_size) {
                    // Create upload directory if it doesn't exist
                    $upload_dir = __DIR__ . '/../assets/images';

                    if (!is_dir($upload_dir)) {
                        if (!mkdir($upload_dir, 0755, true)) {
                            error_log("Failed to create upload directory: " . $upload_dir);
                            set_flash_message('error', 'Failed to create upload directory. Please check server permissions.');
                            redirect('settings.php?category=appearance');
                            exit;
                        }
                    }

                    // Check if directory is writable
                    if (!is_writable($upload_dir)) {
                        error_log("Upload directory is not writable: " . $upload_dir);
                        set_flash_message('error', 'Upload directory is not writable. Please check server permissions.');
                        redirect('settings.php?category=appearance');
                        exit;
                    }

                    // Set file name with timestamp to prevent caching issues
                    $new_file_name = 'logo_' . time() . '.' . $file_ext;
                    $file_path = '/assets/images/' . $new_file_name;
                    $full_path = __DIR__ . '/..' . $file_path;

                    // Move uploaded file
                    if (move_uploaded_file($file_tmp, $full_path)) {
                        // Update setting
                        update_setting('site_logo', $file_path);

                        // Log success for debugging
                        error_log("Logo uploaded successfully to: " . $full_path);
                        error_log("Logo path saved in settings: " . $file_path);

                        set_flash_message('success', 'Logo uploaded successfully.');
                    } else {
                        // Log error for debugging
                        error_log("Failed to upload logo. Error code: " . $_FILES['logo_file']['error']);
                        error_log("Attempted to move from: " . $file_tmp . " to: " . $full_path);

                        set_flash_message('error', 'Error uploading file. Please try again. Error code: ' . $_FILES['logo_file']['error']);
                    }
                } else {
                    set_flash_message('error', 'File size exceeds the maximum limit (2MB).');
                }
            } else {
                set_flash_message('error', 'Invalid file type. Allowed types: ' . implode(', ', $allowed_extensions));
            }
        } else {
            set_flash_message('error', 'Please select a file to upload.');
        }

        redirect('settings.php?category=appearance');
    } elseif (isset($_POST['upload_favicon'])) {
        // Handle favicon upload
        if (isset($_FILES['favicon_file']) && $_FILES['favicon_file']['error'] === UPLOAD_ERR_OK) {
            $file_name = $_FILES['favicon_file']['name'];
            $file_tmp = $_FILES['favicon_file']['tmp_name'];
            $file_size = $_FILES['favicon_file']['size'];
            $file_type = $_FILES['favicon_file']['type'];

            // Get file extension
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            // Check file extension
            $allowed_extensions = ['svg', 'png', 'ico'];

            if (in_array($file_ext, $allowed_extensions)) {
                // Check file size (1MB max)
                $max_size = 1 * 1024 * 1024;

                if ($file_size <= $max_size) {
                    // Create upload directory if it doesn't exist
                    $upload_dir = __DIR__ . '/../assets/images';

                    if (!is_dir($upload_dir)) {
                        if (!mkdir($upload_dir, 0755, true)) {
                            error_log("Failed to create upload directory: " . $upload_dir);
                            set_flash_message('error', 'Failed to create upload directory. Please check server permissions.');
                            redirect('settings.php?category=appearance');
                            exit;
                        }
                    }

                    // Check if directory is writable
                    if (!is_writable($upload_dir)) {
                        error_log("Upload directory is not writable: " . $upload_dir);
                        set_flash_message('error', 'Upload directory is not writable. Please check server permissions.');
                        redirect('settings.php?category=appearance');
                        exit;
                    }

                    // Set file name with timestamp to prevent caching issues
                    $new_file_name = 'favicon_' . time() . '.' . $file_ext;
                    $file_path = '/assets/images/' . $new_file_name;
                    $full_path = __DIR__ . '/..' . $file_path;

                    // Move uploaded file
                    if (move_uploaded_file($file_tmp, $full_path)) {
                        // Update setting
                        update_setting('site_favicon', $file_path);

                        // Log success for debugging
                        error_log("Favicon uploaded successfully to: " . $full_path);
                        error_log("Favicon path saved in settings: " . $file_path);

                        set_flash_message('success', 'Favicon uploaded successfully.');
                    } else {
                        // Log error for debugging
                        error_log("Failed to upload favicon. Error code: " . $_FILES['favicon_file']['error']);
                        error_log("Attempted to move from: " . $file_tmp . " to: " . $full_path);

                        set_flash_message('error', 'Error uploading file. Please try again. Error code: ' . $_FILES['favicon_file']['error']);
                    }
                } else {
                    set_flash_message('error', 'File size exceeds the maximum limit (1MB).');
                }
            } else {
                set_flash_message('error', 'Invalid file type. Allowed types: ' . implode(', ', $allowed_extensions));
            }
        } else {
            set_flash_message('error', 'Please select a file to upload.');
        }

        redirect('settings.php?category=appearance');
    }
}

// Get settings
$settings = [];
$result = $db->query("SELECT * FROM settings ORDER BY setting_key ASC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="settings-management">
    <div class="page-header">
        <h1>System Settings</h1>
    </div>

    <div class="settings-container">
        <div class="settings-sidebar">
            <ul class="settings-nav">
                <?php foreach ($categories as $key => $name): ?>
                    <li class="<?php echo $active_category === $key ? 'active' : ''; ?>">
                        <a href="settings.php?category=<?php echo $key; ?>">
                            <?php if ($key === 'general'): ?>
                                <i class="fas fa-cog"></i>
                            <?php elseif ($key === 'appearance'): ?>
                                <i class="fas fa-palette"></i>
                            <?php elseif ($key === 'email'): ?>
                                <i class="fas fa-envelope"></i>
                            <?php elseif ($key === 'security'): ?>
                                <i class="fas fa-shield-alt"></i>
                            <?php endif; ?>
                            <?php echo $name; ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <div class="settings-content">
            <div class="card">
                <div class="card-header">
                    <h2><?php echo $categories[$active_category]; ?></h2>
                </div>
                <div class="card-content">
                    <?php if ($active_category === 'general'): ?>
                        <form method="POST" action="settings.php?category=general">
                            <?php echo csrf_token_field(); ?>
                            <input type="hidden" name="update_settings" value="1">

                            <div class="form-group">
                                <label for="site_name">Site Name</label>
                                <input type="text" id="site_name" name="settings[site_name]" class="form-control" value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="site_tagline">Site Tagline</label>
                                <input type="text" id="site_tagline" name="settings[site_tagline]" class="form-control" value="<?php echo htmlspecialchars($settings['site_tagline'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="company_email">Company Email</label>
                                <input type="email" id="company_email" name="settings[company_email]" class="form-control" value="<?php echo htmlspecialchars($settings['company_email'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="company_phone">Company Phone</label>
                                <input type="text" id="company_phone" name="settings[company_phone]" class="form-control" value="<?php echo htmlspecialchars($settings['company_phone'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="company_address">Company Address</label>
                                <textarea id="company_address" name="settings[company_address]" class="form-control" rows="3"><?php echo htmlspecialchars($settings['company_address'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="button button-primary">Save Changes</button>
                            </div>
                        </form>
                    <?php elseif ($active_category === 'appearance'): ?>
                        <div class="settings-section">
                            <h3>Logo Settings</h3>

                            <h4>Current Logo</h4>
                            <div class="logo-preview">
                                <div class="preview-box">
                                    <?php if (isset($settings['site_logo']) && !empty($settings['site_logo'])): ?>
                                        <img src="<?php echo BASE_URL . $settings['site_logo']; ?>" alt="Site Logo">
                                        <div class="preview-info">
                                            <span class="preview-path"><?php echo $settings['site_logo']; ?></span>
                                            <a href="<?php echo BASE_URL . $settings['site_logo']; ?>" target="_blank" class="preview-link">
                                                <i class="fas fa-external-link-alt"></i> View Full Size
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-logo">
                                            <i class="fas fa-image"></i>
                                            <span>No logo uploaded</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <form method="POST" action="settings.php?category=appearance" enctype="multipart/form-data">
                                    <?php echo csrf_token_field(); ?>
                                    <input type="hidden" name="upload_logo" value="1">

                                    <div class="file-upload">
                                        <input type="file" name="logo_file" id="logo_file" class="file-input" accept=".svg,.png,.jpg,.jpeg">
                                        <label for="logo_file" class="file-label">
                                            <i class="fas fa-upload"></i> Choose Logo File
                                        </label>
                                        <span class="file-name">No file chosen</span>
                                    </div>

                                    <div class="upload-info">
                                        <p><i class="fas fa-info-circle"></i> Recommended size: 200x50 pixels. Supported formats: SVG, PNG, JPG.</p>
                                    </div>

                                    <button type="submit" class="button button-primary">Upload Logo</button>
                                </form>
                            </div>

                            <h4>Logo Size</h4>
                            <form method="POST" action="settings.php?category=appearance">
                                <?php echo csrf_token_field(); ?>
                                <input type="hidden" name="update_settings" value="1">

                                <div class="form-group">
                                    <label for="logo_size">Logo Size</label>
                                    <select id="logo_size" name="settings[logo_size]" class="form-control">
                                        <option value="small" <?php echo ($settings['logo_size'] ?? 'medium') == 'small' ? 'selected' : ''; ?>>Small</option>
                                        <option value="medium" <?php echo ($settings['logo_size'] ?? 'medium') == 'medium' ? 'selected' : ''; ?>>Medium</option>
                                        <option value="large" <?php echo ($settings['logo_size'] ?? 'medium') == 'large' ? 'selected' : ''; ?>>Large</option>
                                    </select>
                                    <p class="form-help">Choose the size of the logo displayed on the website.</p>
                                </div>

                                <div class="form-group">
                                    <label for="logo_custom_size">Custom Size (percentage)</label>
                                    <div class="range-slider-container">
                                        <input type="range" id="logo_custom_size" name="settings[logo_custom_size]"
                                               min="50" max="200" step="5"
                                               value="<?php echo htmlspecialchars($settings['logo_custom_size'] ?? '100'); ?>"
                                               class="range-slider">
                                        <span class="range-value"><?php echo htmlspecialchars($settings['logo_custom_size'] ?? '100'); ?>%</span>
                                    </div>
                                    <p class="form-help">Adjust the logo size as a percentage of its original size.</p>
                                </div>

                                <div class="form-group">
                                    <label for="show_site_name">Show Site Name</label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="show_site_name" name="settings[show_site_name]" class="toggle-input" <?php echo ($settings['show_site_name'] ?? '0') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="show_site_name" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[show_site_name]" value="0">
                                    </div>
                                    <p class="form-help">Show the site name next to the logo.</p>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="button button-primary">Save Logo Settings</button>
                                </div>
                            </form>
                        </div>

                        <div class="settings-section">
                            <h3>Favicon Settings</h3>

                            <h4>Current Favicon</h4>
                            <div class="logo-preview">
                                <div class="preview-box favicon-preview">
                                    <?php if (isset($settings['site_favicon']) && !empty($settings['site_favicon'])): ?>
                                        <img src="<?php echo BASE_URL . $settings['site_favicon']; ?>" alt="Site Favicon">
                                        <div class="preview-info">
                                            <span class="preview-path"><?php echo $settings['site_favicon']; ?></span>
                                            <a href="<?php echo BASE_URL . $settings['site_favicon']; ?>" target="_blank" class="preview-link">
                                                <i class="fas fa-external-link-alt"></i> View Full Size
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-logo">
                                            <i class="fas fa-image"></i>
                                            <span>No favicon uploaded</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <form method="POST" action="settings.php?category=appearance" enctype="multipart/form-data">
                                    <?php echo csrf_token_field(); ?>
                                    <input type="hidden" name="upload_favicon" value="1">

                                    <div class="file-upload">
                                        <input type="file" name="favicon_file" id="favicon_file" class="file-input" accept=".svg,.png,.ico">
                                        <label for="favicon_file" class="file-label">
                                            <i class="fas fa-upload"></i> Choose Favicon File
                                        </label>
                                        <span class="file-name">No file chosen</span>
                                    </div>

                                    <div class="upload-info">
                                        <p><i class="fas fa-info-circle"></i> Recommended size: 32x32 or 64x64 pixels. Supported formats: ICO, PNG, SVG.</p>
                                    </div>

                                    <button type="submit" class="button button-primary">Upload Favicon</button>
                                </form>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h3>Color Settings</h3>

                            <form method="POST" action="settings.php?category=appearance">
                                <?php echo csrf_token_field(); ?>
                                <input type="hidden" name="update_settings" value="1">

                                <div class="form-group">
                                    <label for="primary_color">Primary Color</label>
                                    <div class="color-picker">
                                        <input type="color" id="primary_color" name="settings[primary_color]" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#4f46e5'); ?>">
                                        <input type="text" class="color-value" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#4f46e5'); ?>" readonly>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="secondary_color">Secondary Color</label>
                                    <div class="color-picker">
                                        <input type="color" id="secondary_color" name="settings[secondary_color]" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6b7280'); ?>">
                                        <input type="text" class="color-value" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#6b7280'); ?>" readonly>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="button button-primary">Save Changes</button>
                                </div>
                            </form>
                        </div>

                        <div class="settings-section">
                            <h3>Dashboard Visibility Settings</h3>
                            <p class="description">Control which sections are visible on the user dashboard.</p>

                            <form method="POST" action="settings.php?category=appearance">
                                <?php echo csrf_token_field(); ?>
                                <input type="hidden" name="update_settings" value="1">

                                <div class="form-group toggle-group">
                                    <label for="show_loan_journey">
                                        <span class="toggle-label">"Your Loan Journey" Section</span>
                                        <span class="toggle-description">Shows vertical progress bars with loan status counts</span>
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="show_loan_journey" name="settings[show_loan_journey]" class="toggle-input" <?php echo ($settings['show_loan_journey'] ?? '1') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="show_loan_journey" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[show_loan_journey]" value="0">
                                    </div>
                                </div>

                                <div class="form-group toggle-group">
                                    <label for="show_loan_applications">
                                        <span class="toggle-label">"My Loan Applications" Section</span>
                                        <span class="toggle-description">Shows recent loan applications table</span>
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="show_loan_applications" name="settings[show_loan_applications]" class="toggle-input" <?php echo ($settings['show_loan_applications'] ?? '1') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="show_loan_applications" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[show_loan_applications]" value="0">
                                    </div>
                                </div>

                                <div class="form-group toggle-group">
                                    <label for="show_recent_documents">
                                        <span class="toggle-label">"Recent Documents" Section</span>
                                        <span class="toggle-description">Shows recent uploaded documents table</span>
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="show_recent_documents" name="settings[show_recent_documents]" class="toggle-input" <?php echo ($settings['show_recent_documents'] ?? '1') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="show_recent_documents" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[show_recent_documents]" value="0">
                                    </div>
                                </div>

                                <div class="form-group toggle-group">
                                    <label for="show_recent_transactions">
                                        <span class="toggle-label">"Recent Transactions" Section</span>
                                        <span class="toggle-description">Shows recent financial transactions table</span>
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="show_recent_transactions" name="settings[show_recent_transactions]" class="toggle-input" <?php echo ($settings['show_recent_transactions'] ?? '1') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="show_recent_transactions" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[show_recent_transactions]" value="0">
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="button button-primary">Save Changes</button>
                                </div>
                            </form>
                        </div>

                        <div class="settings-section">
                            <h3>Google Translate Settings</h3>
                            <p class="description">Control the visibility of Google Translate widgets across your website. The system supports 18 languages including English, Spanish, French, German, Chinese, Portuguese, Russian, Arabic, Italian, Japanese, Korean, Hindi, Dutch, Swedish, Norwegian, Danish, Finnish, and Thai.</p>

                            <form method="POST" action="settings.php?category=appearance">
                                <?php echo csrf_token_field(); ?>
                                <input type="hidden" name="update_settings" value="1">

                                <div class="form-group toggle-group">
                                    <label for="google_translate_header_enabled">
                                        <span class="toggle-label">Universal Header Translation</span>
                                        <span class="toggle-description">Enable Google Translate widget on all pages that use the universal header (home, about, services, contact, etc.)</span>
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="google_translate_header_enabled" name="settings[google_translate_header_enabled]" class="toggle-input" <?php echo ($settings['google_translate_header_enabled'] ?? '1') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="google_translate_header_enabled" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[google_translate_header_enabled]" value="0">
                                    </div>
                                </div>

                                <div class="form-group toggle-group">
                                    <label for="google_translate_login_enabled">
                                        <span class="toggle-label">Login Page Translation</span>
                                        <span class="toggle-description">Enable Google Translate widget specifically on the login page</span>
                                    </label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="google_translate_login_enabled" name="settings[google_translate_login_enabled]" class="toggle-input" <?php echo ($settings['google_translate_login_enabled'] ?? '1') == '1' ? 'checked' : ''; ?> value="1">
                                        <label for="google_translate_login_enabled" class="toggle-label-switch"></label>
                                        <input type="hidden" name="settings[google_translate_login_enabled]" value="0">
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="button button-primary">Save Translation Settings</button>
                                </div>
                            </form>
                        </div>
                    <?php elseif ($active_category === 'email'): ?>
                        <div class="email-settings-tabs">
                            <ul class="tabs-nav">
                                <li class="active" data-tab="general-settings">
                                    <a href="#general-settings">General Settings</a>
                                </li>
                                <li data-tab="smtp-settings">
                                    <a href="#smtp-settings">SMTP Settings</a>
                                </li>
                                <li data-tab="test-smtp">
                                    <a href="#test-smtp">Test SMTP</a>
                                </li>
                                <li data-tab="test-templates">
                                    <a href="#test-templates">Test Templates</a>
                                </li>
                            </ul>
                        </div>

                        <div class="tab-content active" id="general-settings-tab">
                            <form method="POST" action="settings.php?category=email">
                                <?php echo csrf_token_field(); ?>
                                <input type="hidden" name="update_settings" value="1">

                                <div class="settings-section">
                                    <h3>General Email Settings</h3>

                                <div class="form-group">
                                    <label for="email_from_name">From Name</label>
                                    <input type="text" id="email_from_name" name="settings[email_from_name]" class="form-control" value="<?php echo htmlspecialchars($settings['email_from_name'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="email_from_address">From Email Address</label>
                                    <input type="email" id="email_from_address" name="settings[email_from_address]" class="form-control" value="<?php echo htmlspecialchars($settings['email_from_address'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="email_footer_text">Email Footer Text</label>
                                    <textarea id="email_footer_text" name="settings[email_footer_text]" class="form-control" rows="3"><?php echo htmlspecialchars($settings['email_footer_text'] ?? ''); ?></textarea>
                                </div>
                            </div>

                                <div class="form-actions">
                                    <button type="submit" class="button button-primary">Save Changes</button>
                                </div>
                            </form>
                        </div>

                        <div class="tab-content" id="smtp-settings-tab">
                            <form method="POST" action="settings.php?category=email">
                                <?php echo csrf_token_field(); ?>
                                <input type="hidden" name="update_settings" value="1">

                                <div class="settings-section">
                                    <h3>SMTP Settings</h3>

                                    <div class="form-group">
                                        <label for="use_smtp">Use SMTP</label>
                                        <select id="use_smtp" name="settings[use_smtp]" class="form-control">
                                            <option value="0" <?php echo ($settings['use_smtp'] ?? '0') == '0' ? 'selected' : ''; ?>>No (Use PHP mail function)</option>
                                            <option value="1" <?php echo ($settings['use_smtp'] ?? '0') == '1' ? 'selected' : ''; ?>>Yes (Use SMTP server)</option>
                                        </select>
                                    </div>

                                    <div id="smtp-settings-fields" style="<?php echo ($settings['use_smtp'] ?? '0') == '0' ? 'display: none;' : ''; ?>">
                                        <div class="form-group">
                                            <label for="smtp_host">SMTP Host</label>
                                            <input type="text" id="smtp_host" name="settings[smtp_host]" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_host'] ?? 'smtp.hostinger.com'); ?>">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_port">SMTP Port</label>
                                            <input type="text" id="smtp_port" name="settings[smtp_port]" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '465'); ?>">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_secure">SMTP Security</label>
                                            <select id="smtp_secure" name="settings[smtp_secure]" class="form-control">
                                                <option value="ssl" <?php echo ($settings['smtp_secure'] ?? 'ssl') == 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                                <option value="tls" <?php echo ($settings['smtp_secure'] ?? 'ssl') == 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="" <?php echo ($settings['smtp_secure'] ?? 'ssl') == '' ? 'selected' : ''; ?>>None</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_username">SMTP Username</label>
                                            <input type="text" id="smtp_username" name="settings[smtp_username]" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_username'] ?? '<EMAIL>'); ?>">
                                        </div>

                                        <div class="form-group">
                                            <label for="smtp_password">SMTP Password</label>
                                            <input type="password" id="smtp_password" name="settings[smtp_password]" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_password'] ?? 'Money2025@Demo#'); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="button button-primary">Save Changes</button>
                                </div>
                            </form>
                        </div>

                        <div class="tab-content" id="test-smtp-tab">
                            <div class="settings-section">
                                <h3>Test SMTP Configuration</h3>
                                <p class="description">
                                    Use this form to test your SMTP configuration by sending a test email.
                                </p>

                                <form id="test-smtp-form">
                                    <div class="form-group">
                                        <label for="smtp_test_host">SMTP Host</label>
                                        <input type="text" id="smtp_test_host" name="smtp_host" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_host'] ?? 'smtp.hostinger.com'); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_port">SMTP Port</label>
                                        <input type="text" id="smtp_test_port" name="smtp_port" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '465'); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_secure">SMTP Security</label>
                                        <select id="smtp_test_secure" name="smtp_secure" class="form-control">
                                            <option value="ssl" <?php echo ($settings['smtp_secure'] ?? 'ssl') == 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                            <option value="tls" <?php echo ($settings['smtp_secure'] ?? 'ssl') == 'tls' ? 'selected' : ''; ?>>TLS</option>
                                            <option value="" <?php echo ($settings['smtp_secure'] ?? 'ssl') == '' ? 'selected' : ''; ?>>None</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_username">SMTP Username</label>
                                        <input type="text" id="smtp_test_username" name="smtp_username" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_username'] ?? '<EMAIL>'); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_password">SMTP Password</label>
                                        <input type="password" id="smtp_test_password" name="smtp_password" class="form-control" value="<?php echo htmlspecialchars($settings['smtp_password'] ?? 'Money2025@Demo#'); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_from_email">From Email</label>
                                        <input type="email" id="smtp_test_from_email" name="from_email" class="form-control" value="<?php echo htmlspecialchars($settings['email_from_address'] ?? '<EMAIL>'); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_from_name">From Name</label>
                                        <input type="text" id="smtp_test_from_name" name="from_name" class="form-control" value="<?php echo htmlspecialchars($settings['email_from_name'] ?? 'LendSwift'); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="smtp_test_to_email">Test Email Address</label>
                                        <input type="email" id="smtp_test_to_email" name="test_email" class="form-control" value="<EMAIL>" required>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="button button-primary">
                                            <i class="fas fa-paper-plane"></i> Send Test Email
                                        </button>
                                    </div>
                                </form>

                                <div id="smtp-test-result" class="test-result" style="display: none;"></div>
                            </div>
                        </div>

                        <div class="tab-content" id="test-templates-tab">
                            <div class="settings-section">
                                <h3>Test Email Templates</h3>
                                <p class="description">
                                    Use this form to test an email template by sending a test email.
                                    The template variables will be replaced with sample data.
                                </p>

                                <form id="test-template-form">
                                    <div class="form-group">
                                        <label for="template_id">Email Template</label>
                                        <select id="template_id" name="template_id" class="form-control" required>
                                            <option value="">Select a template</option>
                                            <?php
                                            $templates_result = $db->query("SELECT id, name FROM email_templates ORDER BY name");
                                            if ($templates_result && $templates_result->num_rows > 0) {
                                                while ($template = $templates_result->fetch_assoc()) {
                                                    echo '<option value="' . $template['id'] . '">' . htmlspecialchars($template['name']) . '</option>';
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="template_test_email">Test Email Address</label>
                                        <input type="email" id="template_test_email" name="test_email" class="form-control" value="<EMAIL>" required>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="button button-primary">
                                            <i class="fas fa-paper-plane"></i> Send Test Email
                                        </button>
                                    </div>
                                </form>

                                <div id="template-test-result" class="test-result" style="display: none;"></div>
                            </div>
                        </div>

                        <div id="test-email-modal" class="modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>Send Test Email</h3>
                                    <button type="button" class="close-modal">&times;</button>
                                </div>
                                <div class="modal-body">
                                    <form id="test-email-form">
                                        <div class="form-group">
                                            <label for="test-email-address">Email Address</label>
                                            <input type="email" id="test-email-address" name="email" class="form-control" value="<EMAIL>" required>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="button button-primary">
                                                <i class="fas fa-paper-plane"></i> Send
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php elseif ($active_category === 'security'): ?>
                        <form method="POST" action="settings.php?category=security">
                            <?php echo csrf_token_field(); ?>
                            <input type="hidden" name="update_settings" value="1">

                            <div class="form-group">
                                <label for="max_login_attempts">Max Login Attempts</label>
                                <input type="number" id="max_login_attempts" name="settings[max_login_attempts]" class="form-control" value="<?php echo htmlspecialchars($settings['max_login_attempts'] ?? '5'); ?>" min="1" max="10">
                            </div>

                            <div class="form-group">
                                <label for="session_lifetime">Session Lifetime (seconds)</label>
                                <input type="number" id="session_lifetime" name="settings[session_lifetime]" class="form-control" value="<?php echo htmlspecialchars($settings['session_lifetime'] ?? '7200'); ?>" min="300">
                            </div>

                            <div class="form-group">
                                <label for="enable_password_reset">Enable Password Reset</label>
                                <select id="enable_password_reset" name="settings[enable_password_reset]" class="form-control">
                                    <option value="1" <?php echo ($settings['enable_password_reset'] ?? '1') == '1' ? 'selected' : ''; ?>>Yes</option>
                                    <option value="0" <?php echo ($settings['enable_password_reset'] ?? '1') == '0' ? 'selected' : ''; ?>>No</option>
                                </select>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="button button-primary">Save Changes</button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .settings-management {
        margin-bottom: 2rem;
    }

    .page-header {
        margin-bottom: 1.5rem;
    }

    .settings-container {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 1.5rem;
    }

    .settings-sidebar {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .settings-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .settings-nav li {
        border-bottom: 1px solid #e5e7eb;
    }

    .settings-nav li:last-child {
        border-bottom: none;
    }

    .settings-nav li a {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .settings-nav li a i {
        margin-right: 0.75rem;
        width: 1.25rem;
        text-align: center;
    }

    .settings-nav li a:hover {
        background-color: #f9fafb;
        color: #4f46e5;
    }

    .settings-nav li.active a {
        background-color: #f9fafb;
        color: #4f46e5;
        font-weight: 500;
    }

    /* Range slider styles */
    .range-slider-container {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .range-slider {
        flex: 1;
        height: 5px;
        -webkit-appearance: none;
        appearance: none;
        background: #e5e7eb;
        outline: none;
        border-radius: 5px;
    }

    .range-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #4f46e5;
        cursor: pointer;
        border: 2px solid white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .range-slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #4f46e5;
        cursor: pointer;
        border: 2px solid white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .range-value {
        font-weight: 600;
        min-width: 50px;
        text-align: center;
    }

    .form-help {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.5rem;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .settings-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .settings-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .settings-section h3 {
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.125rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .form-actions {
        margin-top: 1.5rem;
    }

    .button {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background-color: #4f46e5;
        color: #fff;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .button:hover {
        background-color: #4338ca;
    }

    .logo-preview {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .preview-container {
        margin-bottom: 1.5rem;
    }

    .preview-container h4 {
        margin-top: 0;
        margin-bottom: 0.75rem;
        font-size: 1rem;
        color: #4b5563;
    }

    .preview-box {
        width: 100%;
        height: 150px;
        border: 2px dashed #d1d5db;
        border-radius: 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background-color: #f9fafb;
        position: relative;
        margin-bottom: 1rem;
    }

    .preview-box img {
        max-width: 100%;
        max-height: 70%;
        object-fit: contain;
    }

    .preview-info {
        width: 100%;
        padding: 0.5rem;
        background-color: rgba(249, 250, 251, 0.9);
        font-size: 0.75rem;
        text-align: center;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-top: 1px solid #e5e7eb;
    }

    /* Make sure the preview container is visible */
    .preview-container {
        margin-bottom: 1.5rem;
        width: 100%;
        display: block;
    }

    .preview-path {
        display: block;
        color: #6b7280;
        margin-bottom: 0.25rem;
        font-family: monospace;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .preview-link {
        color: #4f46e5;
        text-decoration: none;
        font-size: 0.75rem;
    }

    .preview-link:hover {
        text-decoration: underline;
    }

    .favicon-preview {
        height: 120px;
        background-color: #f0f4f8;
    }

    .favicon-preview img {
        max-width: 64px;
        max-height: 64px;
        margin-top: 10px;
        border: 1px solid #e5e7eb;
        background-color: white;
        padding: 5px;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .no-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #9ca3af;
    }

    .no-logo i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .upload-info {
        margin-top: 0.5rem;
        margin-bottom: 1rem;
    }

    .upload-info p {
        font-size: 0.75rem;
        color: #6b7280;
        margin: 0;
    }

    .file-upload {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .file-input {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        border: 0;
    }

    .file-label {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        cursor: pointer;
        margin-right: 0.5rem;
        font-size: 0.875rem;
    }

    .file-name {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .color-picker {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .color-picker input[type="color"] {
        width: 50px;
        height: 40px;
        padding: 0;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        cursor: pointer;
    }

    .color-value {
        width: 100px;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-family: monospace;
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .modal-content {
        position: relative;
        margin: 10% auto;
        width: 500px;
        max-width: 90%;
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .modal-header h3 {
        margin: 0;
    }

    .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6b7280;
    }

    .modal-body {
        padding: 1rem;
    }

    /* Email Settings Tabs */
    .email-settings-tabs {
        margin-bottom: 1.5rem;
    }

    .tabs-nav {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .tabs-nav li {
        margin-right: 0.5rem;
        margin-bottom: -1px;
    }

    .tabs-nav li a {
        display: block;
        padding: 0.75rem 1.25rem;
        color: #6b7280;
        text-decoration: none;
        border: 1px solid transparent;
        border-top-left-radius: 0.375rem;
        border-top-right-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .tabs-nav li a:hover {
        color: #4f46e5;
        background-color: #f9fafb;
    }

    .tabs-nav li.active a {
        color: #4f46e5;
        background-color: #fff;
        border-color: #e5e7eb;
        border-bottom-color: #fff;
        font-weight: 500;
    }

    .tab-content {
        display: none;
        padding: 1.5rem 0;
    }

    .tab-content.active {
        display: block;
    }

    .test-result {
        margin-top: 1.5rem;
        padding: 1rem;
        border-radius: 0.375rem;
    }

    .test-result.success {
        background-color: #d1fae5;
        color: #047857;
        border: 1px solid #a7f3d0;
    }

    .test-result.error {
        background-color: #fee2e2;
        color: #b91c1c;
        border: 1px solid #fecaca;
    }

    .test-result pre {
        margin-top: 1rem;
        padding: 1rem;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 0.25rem;
        overflow: auto;
        font-family: monospace;
        font-size: 0.85rem;
        white-space: pre-wrap;
    }

    /* Toggle Switch Styles */
    .toggle-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: #f9fafb;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        transition: background-color 0.2s ease;
    }

    .toggle-group:hover {
        background-color: #f3f4f6;
    }

    .toggle-group label {
        margin-bottom: 0;
        display: flex;
        flex-direction: column;
        cursor: pointer;
    }

    .toggle-label {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .toggle-description {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: normal;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        flex-shrink: 0;
    }

    .toggle-input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-label-switch {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #e5e7eb;
        transition: .4s;
        border-radius: 24px;
    }

    .toggle-label-switch:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    .toggle-input:checked + .toggle-label-switch {
        background-color: #4f46e5;
    }

    .toggle-input:focus + .toggle-label-switch {
        box-shadow: 0 0 1px #4f46e5;
    }

    .toggle-input:checked + .toggle-label-switch:before {
        transform: translateX(26px);
    }

    .description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    @media (max-width: 992px) {
        .settings-container {
            grid-template-columns: 1fr;
        }

        .settings-nav {
            display: flex;
            overflow-x: auto;
        }

        .settings-nav li {
            border-bottom: none;
            border-right: 1px solid #e5e7eb;
        }

        .settings-nav li:last-child {
            border-right: none;
        }

        .tabs-nav {
            overflow-x: auto;
        }

        .toggle-group {
            flex-direction: column;
            align-items: flex-start;
        }

        .toggle-switch {
            margin-top: 0.75rem;
        }

        .tabs-nav {
            white-space: nowrap;
            padding-bottom: 0.5rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // File upload
        const fileInputs = document.querySelectorAll('.file-input');

        fileInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                const fileName = this.files[0] ? this.files[0].name : 'No file chosen';
                const fileNameElement = this.parentElement.querySelector('.file-name');

                if (fileNameElement) {
                    fileNameElement.textContent = fileName;
                }
            });
        });

        // Toggle switches
        const toggleInputs = document.querySelectorAll('.toggle-input');

        toggleInputs.forEach(function(input) {
            // Find the hidden input with the same name
            const hiddenInput = input.nextElementSibling.nextElementSibling;

            // Set initial state
            if (hiddenInput) {
                hiddenInput.disabled = input.checked;
            }

            // Handle change event
            input.addEventListener('change', function() {
                if (hiddenInput) {
                    hiddenInput.disabled = this.checked;
                }

                // Add visual feedback
                const toggleGroup = this.closest('.toggle-group');
                if (toggleGroup) {
                    toggleGroup.style.backgroundColor = this.checked ? '#f0f9ff' : '#f9fafb';
                    setTimeout(() => {
                        toggleGroup.style.backgroundColor = '';
                    }, 300);
                }
            });
        });

        // Color picker
        const colorInputs = document.querySelectorAll('input[type="color"]');

        colorInputs.forEach(function(input) {
            input.addEventListener('input', function() {
                const colorValue = this.parentElement.querySelector('.color-value');

                if (colorValue) {
                    colorValue.value = this.value;
                }
            });
        });

        // SMTP Settings
        const useSmtpSelect = document.getElementById('use_smtp');
        const smtpSettingsFields = document.getElementById('smtp-settings-fields');

        if (useSmtpSelect && smtpSettingsFields) {
            useSmtpSelect.addEventListener('change', function() {
                if (this.value === '1') {
                    smtpSettingsFields.style.display = 'block';
                } else {
                    smtpSettingsFields.style.display = 'none';
                }
            });
        }

        // Email Settings Tabs
        const tabLinks = document.querySelectorAll('.tabs-nav li');
        const tabContents = document.querySelectorAll('.tab-content');

        if (tabLinks.length > 0) {
            tabLinks.forEach(function(tabLink) {
                tabLink.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all tabs
                    tabLinks.forEach(function(link) {
                        link.classList.remove('active');
                    });

                    // Hide all tab contents
                    tabContents.forEach(function(content) {
                        content.classList.remove('active');
                    });

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show corresponding tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId + '-tab').classList.add('active');
                });
            });
        }

        // Test SMTP Form
        const testSmtpForm = document.getElementById('test-smtp-form');
        const smtpTestResult = document.getElementById('smtp-test-result');

        if (testSmtpForm) {
            testSmtpForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                submitBtn.disabled = true;

                // Clear previous results
                smtpTestResult.innerHTML = '';
                smtpTestResult.className = 'test-result';
                smtpTestResult.style.display = 'none';

                // Get form data
                const formData = new FormData(this);

                // Send AJAX request
                fetch('../test/ajax/test_smtp.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show result
                    smtpTestResult.style.display = 'block';

                    if (data.success) {
                        smtpTestResult.className = 'test-result success';
                        smtpTestResult.innerHTML = '<i class="fas fa-check-circle"></i> ' + data.message;

                        if (data.debug) {
                            smtpTestResult.innerHTML += '<pre>' + data.debug + '</pre>';
                        }
                    } else {
                        smtpTestResult.className = 'test-result error';
                        smtpTestResult.innerHTML = '<i class="fas fa-exclamation-circle"></i> ' + data.message;

                        if (data.debug) {
                            smtpTestResult.innerHTML += '<pre>' + data.debug + '</pre>';
                        }
                    }
                })
                .catch(error => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show error
                    smtpTestResult.style.display = 'block';
                    smtpTestResult.className = 'test-result error';
                    smtpTestResult.innerHTML = '<i class="fas fa-exclamation-circle"></i> An error occurred while testing SMTP. Please try again.';
                    console.error('Error:', error);
                });
            });
        }

        // Test Template Form
        const testTemplateForm = document.getElementById('test-template-form');
        const templateTestResult = document.getElementById('template-test-result');

        if (testTemplateForm) {
            testTemplateForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                submitBtn.disabled = true;

                // Clear previous results
                templateTestResult.innerHTML = '';
                templateTestResult.className = 'test-result';
                templateTestResult.style.display = 'none';

                // Get form data
                const formData = new FormData(this);

                // Send AJAX request
                fetch('../test/ajax/test_template.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show result
                    templateTestResult.style.display = 'block';

                    if (data.success) {
                        templateTestResult.className = 'test-result success';
                        templateTestResult.innerHTML = '<i class="fas fa-check-circle"></i> ' + data.message;
                    } else {
                        templateTestResult.className = 'test-result error';
                        templateTestResult.innerHTML = '<i class="fas fa-exclamation-circle"></i> ' + data.message;
                    }
                })
                .catch(error => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show error
                    templateTestResult.style.display = 'block';
                    templateTestResult.className = 'test-result error';
                    templateTestResult.innerHTML = '<i class="fas fa-exclamation-circle"></i> An error occurred while testing the template. Please try again.';
                    console.error('Error:', error);
                });
            });
        }

        // Test Email
        const testEmailBtn = document.getElementById('test-email-btn');
        const testEmailModal = document.getElementById('test-email-modal');
        const closeModalBtn = document.querySelector('.close-modal');
        const testEmailForm = document.getElementById('test-email-form');

        if (testEmailBtn && testEmailModal) {
            testEmailBtn.addEventListener('click', function() {
                testEmailModal.style.display = 'block';
            });
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                testEmailModal.style.display = 'none';
            });
        }

        window.addEventListener('click', function(event) {
            if (event.target === testEmailModal) {
                testEmailModal.style.display = 'none';
            }
        });

        if (testEmailForm) {
            testEmailForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const email = document.getElementById('test-email-address').value;
                const smtpHost = document.getElementById('smtp_host').value;
                const smtpPort = document.getElementById('smtp_port').value;
                const smtpSecure = document.getElementById('smtp_secure').value;
                const smtpUsername = document.getElementById('smtp_username').value;
                const smtpPassword = document.getElementById('smtp_password').value;

                // Show loading state
                const submitBtn = testEmailForm.querySelector('button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                submitBtn.disabled = true;

                // Prepare form data
                const formData = new FormData();
                formData.append('email', email);
                formData.append('use_smtp', document.getElementById('use_smtp').value);
                formData.append('smtp_host', smtpHost);
                formData.append('smtp_port', smtpPort);
                formData.append('smtp_secure', smtpSecure);
                formData.append('smtp_username', smtpUsername);
                formData.append('smtp_password', smtpPassword);
                formData.append('from_name', document.getElementById('email_from_name').value);
                formData.append('from_email', document.getElementById('email_from_address').value);

                // Send AJAX request
                fetch('ajax/send_test_email.php', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show result
                    if (data.success) {
                        alert('Success: ' + data.message);
                        testEmailModal.style.display = 'none';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;

                    // Show error
                    console.error('Error:', error);
                    alert('An error occurred while sending the test email. Please try again.');
                });
            });
        }
    });

    // Logo size range slider functionality
    const logoSizeSlider = document.getElementById('logo_custom_size');
    const logoSizeValue = document.querySelector('.range-value');

    if (logoSizeSlider && logoSizeValue) {
        // Update the value display when slider changes
        logoSizeSlider.addEventListener('input', function() {
            logoSizeValue.textContent = this.value + '%';
        });
    }
</script>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>
